'use client';

import { useState, useEffect, useCallback } from 'react';
import { userService, User, UsersResponse, PaginateQuery, Role, UserFilters } from '../../services/userService';
import { departmentService, Department } from '../../services/departmentService';
import { roleService } from '../../services/roleService';
import DataTable from '../common/DataTable';
import ConfirmationModal from '../common/ConfirmationModal';
import Select from '../common/Select';
import { Organization, organizationService } from '@/services/organizationService';

interface UsersTabProps {
  onEditUser: (user: User) => void;
  onCreateUser: () => void;
}

const UsersTab = ({ onEditUser, onCreateUser }: UsersTabProps) => {
  const [usersData, setUsersData] = useState<UsersResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [filters, setFilters] = useState<UserFilters>({});
  const [departments, setDepartments] = useState<Department[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [currentQuery, setCurrentQuery] = useState<PaginateQuery>({ page: 1, limit: 10 });

  const loadUsers = useCallback(async (query: PaginateQuery) => {
    try {
      setLoading(true);
      setError(null);
      setCurrentQuery(query);
      const response = await userService.getUsers(query);
      setUsersData(response);
    } catch (err: unknown) {
      console.error('Error loading users:', err);

      // Provide more specific error messages based on the error type
      let errorMessage = 'Failed to load users';

      // Type guards to check for specific error properties
      if (err && typeof err === 'object') {
        // Check for network errors (axios-like errors)
        if ('code' in err && err.code === 'ERR_NETWORK') {
          errorMessage = 'Unable to connect to the server. Please check if the backend is running or contact your administrator.';
        } else if ('message' in err && err.message === 'Network Error') {
          errorMessage = 'Unable to connect to the server. Please check if the backend is running or contact your administrator.';
        } else if ('response' in err && err.response && typeof err.response === 'object') {
          // Handle HTTP response errors
          if ('status' in err.response) {
            const status = err.response.status;
            if (status === 401) {
              errorMessage = 'Authentication required. Please log in again.';
            } else if (status === 403) {
              errorMessage = 'You do not have permission to view users.';
            } else if (status === 500) {
              errorMessage = 'Server error. Please try again later.';
            } else if ('data' in err.response &&
                      err.response.data &&
                      typeof err.response.data === 'object' &&
                      'message' in err.response.data &&
                      typeof err.response.data.message === 'string') {
              errorMessage = err.response.data.message;
            }
          }
        } else if ('message' in err && typeof err.message === 'string') {
          // Fallback to error message if available
          errorMessage = err.message;
        }
      }

      setError(errorMessage);

      // Set empty data structure to prevent undefined errors
      setUsersData({
        data: [],
        meta: {
          itemsPerPage: query.limit || 10,
          totalItems: 0,
          currentPage: query.page || 1,
          totalPages: 0,
          sortBy: [],
          searchBy: [],
          search: '',
          select: [],
        },
        links: {
          current: '',
        },
      });
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadUsers({ page: 1, limit: 10 });
    loadDepartments();
    loadRoles();
    loadOrganizations();
  }, [loadUsers]);

  // Reload data when filters change
  useEffect(() => {
    // Filter out undefined values and ensure proper typing
    const cleanFilters: Record<string, string> = {};
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value.trim() !== '') {
        cleanFilters[key] = value;
      }
    });

    // Always reload with current query and filters
    loadUsers({
      page: 1,
      limit: currentQuery.limit || 10,
      filter: Object.keys(cleanFilters).length > 0 ? cleanFilters : undefined
    });
  }, [filters, loadUsers, currentQuery.limit]);

  const loadDepartments = async () => {
    try {
      const departmentsData = await departmentService.getAllDepartments();
      setDepartments(departmentsData);
    } catch (err) {
      console.error('Error loading departments:', err);
    }
  };

  const loadRoles = async () => {
    try {
      const rolesResponse = await roleService.getRoles({ page: 1, limit: 100 });
      setRoles(rolesResponse.data);
    } catch (err) {
      console.error('Error loading roles:', err);
    }
  };

  const loadOrganizations = async () => {
    try {
      const organizationsResponse = await organizationService.getOrganizations({ page: 1, limit: 100 });
      setOrganizations(organizationsResponse.data);
    } catch (err) {
      console.error('Error loading organizations:', err);
    }
  };

  const handleFilterChange = (key: keyof UserFilters, value: string) => {
    const newFilters = { ...filters };

    if (value && value.trim() !== '') {
      newFilters[key] = value;
    } else {
      delete newFilters[key];
    }

    setFilters(newFilters);
  };



  const handleDeleteUser = (user: User) => {
    setUserToDelete(user);
    setShowDeleteModal(true);
  };

  const handleConfirmDelete = async () => {
    if (!userToDelete) return;

    setIsDeleting(true);
    try {
      await userService.deleteUser(userToDelete.user_id);
      if (usersData) {
        loadUsers({ page: usersData.meta.currentPage, limit: usersData.meta.itemsPerPage });
      }
      setShowDeleteModal(false);
      setUserToDelete(null);
    } catch (err) {
      setError('Failed to delete user');
      console.error('Error deleting user:', err);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleCancelDelete = () => {
    setShowDeleteModal(false);
    setUserToDelete(null);
  };

  // Define columns for users table
  const userColumns = [
    {
      key: 'user',
      label: 'User',
      sortable: true,
      render: (value: unknown, user: User) => (
        <div className="flex items-center">
          <div className="flex-shrink-0 h-10 w-10">
            {user.profile_image ? (
              <img
                className="h-10 w-10 rounded-full"
                src={user.profile_image}
                alt={`${user.first_name} ${user.last_name}`}
              />
            ) : (
              <div className="h-10 w-10 rounded-full bg-red-600 dark:bg-red-700 flex items-center justify-center">
                <span className="text-sm font-medium text-white">
                  {user.first_name.charAt(0)}{user.last_name.charAt(0)}
                </span>
              </div>
            )}
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {user.first_name} {user.last_name}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {user.email}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: 'department',
      label: 'Department',
      render: (value: unknown, user: User) => (
        <span className="text-sm text-gray-900 dark:text-gray-100">
          {user.department ? user.department.name : 'No Department'}
        </span>
      ),
    },
    {
      key: 'roles',
      label: 'Roles',
      render: (value: Role[] | undefined, user: User) => (
        <span className="text-sm text-gray-900 dark:text-gray-100">
          {user.roles && user.roles.length > 0
            ? user.roles.map(role => role?.name).join(', ')
            : 'No Roles'
          }
        </span>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (value: string) => (
        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
          value === 'active'
            ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
            : value === 'suspended'
            ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'
            : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
        }`}>
          {value.charAt(0).toUpperCase() + value.slice(1)}
        </span>
      ),
    },
    {
      key: 'last_login',
      label: 'Last Login',
      sortable: true,
      render: (value: string) => (
        <span className="text-sm text-gray-500 dark:text-gray-400">
          {value ? new Date(value).toLocaleString() : 'Never'}
        </span>
      ),
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (value: unknown, user: User) => (
        <div className="flex items-center space-x-2">
          <button
            onClick={() => onEditUser(user)}
            className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 p-1 rounded hover:bg-indigo-50 dark:hover:bg-indigo-900"
            title="Edit user"
          >
            <i className="ri-edit-line"></i>
          </button>
          <button
            onClick={() => handleDeleteUser(user)}
            className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 p-1 rounded hover:bg-red-50 dark:hover:bg-red-900"
            title="Delete user"
          >
            <i className="ri-delete-bin-line"></i>
          </button>
        </div>
      ),
    },
  ];

  return (
    <div>
      {/* Page header */}
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">Users</h1>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Manage users and their access permissions.
            </p>
          </div>
          <div>
            <div className="flex space-x-2 place-content-start">
              <div className="relative">
                <button
                  type="button"
                  onClick={onCreateUser}
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-900"
                >
                  <div className="w-5 h-5 flex items-center justify-center mr-2">
                    <i className="ri-user-add-line"></i>
                  </div>
                  Add User
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {error && (
        <div className="mb-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {/* Filters Section */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">Filters</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Department Filter */}
          {departments.length > 0 && (
          <Select
            label="Department"
            value={filters.department || ''}
            onChange={(value) => handleFilterChange('department', value)}
            options={[
              { value: '', label: 'All Departments' },
              ...departments.map((department) => ({
                value: department.department_id,
                label: department.name
              }))
            ]}
          />)}

          {/* Organization Filter */}
          {organizations.length > 0 && (
          <Select
            label="Organization"
            value={filters.organization || ''}
            onChange={(value) => handleFilterChange('organization', value)}
            options={[
              { value: '', label: 'All Organizations' },
              ...organizations.map((organization) => ({
                value: organization.organization_id,
                label: organization.name
              }))
            ]}
          />)}

          {/* Role Filter */}
          <Select
            label="Role"
            value={filters.role || ''}
            onChange={(value) => handleFilterChange('role', value)}
            options={[
              { value: '', label: 'All Roles' },
              ...roles.map((role) => ({
                value: role.role_id,
                label: role.name
              }))
            ]}
          />

          {/* Status Filter */}
          <Select
            label="Status"
            value={filters.status || ''}
            onChange={(value) => handleFilterChange('status', value)}
            options={[
              { value: '', label: 'All Statuses' },
              { value: 'active', label: 'Active' },
              { value: 'inactive', label: 'Inactive' },
              { value: 'suspended', label: 'Suspended' }
            ]}
          />
        </div>
      </div>

      <DataTable
        columns={userColumns}
        data={usersData}
        loading={loading}
        onQueryChange={loadUsers}
        searchPlaceholder="Search users by name or email..."
      />

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={showDeleteModal}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title="Delete User"
        message={
          userToDelete ? (
            <div>
              <p className="mb-2">
                Are you sure you want to delete <strong>{userToDelete.first_name} {userToDelete.last_name}</strong>?
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                This action cannot be undone. All data associated with this user will be permanently removed.
              </p>
            </div>
          ) : (
            'Are you sure you want to delete this user?'
          )
        }
        confirmText="Yes, Delete User"
        cancelText="Cancel"
        confirmVariant="danger"
        loading={isDeleting}
      />
    </div>
  );
};

export default UsersTab;
